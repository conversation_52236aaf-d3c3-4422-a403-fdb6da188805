import { AggregateRoot, type Either, left, right } from "@/domain/shared";
import type { BaseEntityProps } from "@/domain/shared/base-entity";
import type { CompanyType, PaymentMethod } from "@/domain/value-objects";
import {
  FinancialCalendar,
  MonthlyYield,
  YieldStatement,
} from "@/domain/value-objects";
import type { ContractType } from "@/domain/value-objects/contract-type.value-object";
import type { Money } from "@/domain/value-objects/money.value-object";
import type { Percentage } from "@/domain/value-objects/percentage.value-object";
import type { Profile } from "@/domain/value-objects/profile.value-object";
import type { Individual } from "../parties";
import type { Investor } from "../user";
import type { ContractAddendum } from "./contract-addenduns.entity";

export enum ContractStatus {
  EXPIRED_BY_AUDIT = "EXPIRED_BY_AUDIT", // EXPIRADO POR NÃO ASSINATURA DA AUDITORIA
  EXPIRED_FAILURE_PROOF_PAYMENT = "EXPIRED_FAILURE_PROOF_PAYMENT", // EXPIRADO POR NÃO INSERIR COMPROVANTE
  DRAFT = "DRAFT", // RASCUNHO
  GENERATED = "GENERATED", // CONTRATO GERADO RASCUNHO
  SIGNATURE_SENT = "SIGNATURE_SENT", // ENVIO DA ASSINATURA
  AWAITING_INVESTOR_SIGNATURE = "AWAITING_INVESTOR_SIGNATURE", // AGUARDANDO ASSINATURA DO INVESTIDOR
  AWAITING_DEPOSIT = "AWAITING_DEPOSIT", // AGUARDANDO COMPROVANTE DE PAGAMENTO
  AWAITING_AUDIT = "AWAITING_AUDIT", // AGUARDANDO ASSINATURA DA AUDITORIA
  AWAITING_AUDIT_SIGNATURE = "AWAITING_AUDIT_SIGNATURE", // AGUARDANDO ASSINATURA DA AUDITORIA
  ACTIVE = "ACTIVE", // ATIVO
  SIGNATURE_FAILED = "SIGNATURE_FAILED", // FALHA AO ENVIAR CONTRATO PARA A ASSINATURA
  EXPIRED_BY_INVESTOR = "EXPIRED_BY_INVESTOR", // EXPIRADO POR FALTA DE ASSINATURA DO CONTRATO
  REJECTED = "REJECTED", // REJEITADO
  REJECTED_BY_AUDIT = "REJECTED_BY_AUDIT", // REJEITADO PELA AUDITORIA
  GENERATE_CONTRACT_FAILED = "GENERATE_CONTRACT_FAILED", // FALHA AO GERAR CONTRATO
  EXPIRED = "EXPIRED", // EXPIRADO
  DELETED = "DELETED", // DELETADO
  AWAITING_UPGRADE = "AWAITING_UPGRADE", // AGUARDANDO UPGRADE - contrato original durante processo de upgrade
  INACTIVE = "INACTIVE", // INATIVO - contrato original após upgrade ser ativado
}
export interface InvestmentContractProps extends BaseEntityProps {
  investor: Investor;
  brokerId: string;
  advisors: { id: string; rate: number }[];
  amount: Money;
  type: ContractType;
  profitability: Percentage;
  paymentMethod: PaymentMethod;
  startDate: Date;
  endDate: Date;
  profile: Profile;
  addendums?: ContractAddendum[];
  isDebenture: boolean;
  proofOfResidence?: string | null;
  companyDocument?: string | null;
  personalDocument?: string | null;
  status?: ContractStatus;
  signatureRequestId?: string;
  contractUrl?: string;
  incomeReport?: {
    year: number;
    fileUrl: string;
  }[];
  contractPdf?: string | null;
  proofPayment?: string | null;
  contractNumber?: number;
  cardCnpj?: string | null;
  quotaQuantity?: number;
  durationInMonths?: number;
}

export class InvestmentContract extends AggregateRoot<InvestmentContractProps> {
  private status: ContractStatus = ContractStatus.DRAFT;

  private constructor(props: InvestmentContractProps, id?: string) {
    super(props, id);
    if (props.status) {
      this.status = props.status;
    }
  }

  static createNew(
    props: Omit<InvestmentContractProps, "startDate">,
    id?: string
  ): Either<Error, InvestmentContract> {
    const startDate = new Date();

    if (props.endDate <= startDate) {
      return left(new Error("End date must be after start date"));
    }
    const contractNumber = Math.floor(10000000 + Math.random() * 90000000);
    return right(
      new InvestmentContract({ ...props, startDate, contractNumber }, id)
    );
  }

  // ✅ Fábrica para contratos pré-existentes (com datas específicas)
  static createFromExisting(
    props: InvestmentContractProps,
    id?: string
  ): Either<Error, InvestmentContract> {
    if (props.endDate <= props.startDate) {
      return left(new Error("End date must be after start date"));
    }

    return right(new InvestmentContract(props, id));
  }

  calculateMonthYield(
    month: number,
    year: number
  ): Either<Error, YieldStatement> {
    // ✅ Get the investment anniversary for this month
    const anniversaryDate = new Date(
      year,
      month - 1,
      this.props.startDate.getDate()
    );
    // ✅ If the contract hasn't started yet, return zero yield
    if (this.props.startDate > anniversaryDate) {
      return right(
        new YieldStatement(
          month,
          year,
          0,
          FinancialCalendar.nextBusinessDay(anniversaryDate)
        )
      );
    }
    const isYearWithinContract = this.isYearWithinContract(
      year,
      this.props.startDate.toString(),
      this.props.endDate.toString()
    );

    if (!isYearWithinContract) {
      return right(
        new YieldStatement(
          month,
          year,
          0,
          FinancialCalendar.nextBusinessDay(anniversaryDate)
        )
      );
    }

    const endAnniversaryDate = this.props.endDate;
    endAnniversaryDate.setDate(endAnniversaryDate.getDate() + 1);

    if (endAnniversaryDate < anniversaryDate) {
      return right(
        new YieldStatement(
          month,
          year,
          0,
          FinancialCalendar.nextBusinessDay(anniversaryDate)
        )
      );
    }

    // ✅ Previous anniversary date (start of the yield period)
    let previousAnniversaryDate = new Date(anniversaryDate);
    previousAnniversaryDate.setMonth(previousAnniversaryDate.getMonth() - 1);

    // ✅ If this is the first month, start from contractStart + 1 day
    if (previousAnniversaryDate < this.props.startDate) {
      previousAnniversaryDate = this.props.startDate;
      previousAnniversaryDate.setDate(previousAnniversaryDate.getDate() + 1); // Apply D+1 rule
    }
    // ✅ Number of active investment days (capped at 30)
    let daysInPeriod =
      (anniversaryDate.getTime() - previousAnniversaryDate.getTime()) /
      (1000 * 60 * 60 * 24);
    if (daysInPeriod > 30) daysInPeriod = 30;

    // ✅ If no valid days, return zero yield
    if (daysInPeriod <= 0)
      return right(
        new YieldStatement(
          month,
          year,
          0,
          FinancialCalendar.nextBusinessDay(anniversaryDate)
        )
      );

    // ✅ Yield Calculation
    const totalYield =
      this.props.amount.amount *
      this.props.profitability.valueAsDecimal *
      (daysInPeriod / 30);
    // ✅ Payment date is the next business day after the anniversary date
    const paymentDate = FinancialCalendar.nextBusinessDay(anniversaryDate);

    return right(new YieldStatement(month, year, totalYield, paymentDate));
  }

  calculateYearYield(year: number) {
    const yearYields = [];
    for (let month = 1; month <= 12; month++) {
      const yieldResult = this.calculateMonthYield(month, year);

      if (yieldResult.isRight()) {
        yearYields.push(
          new MonthlyYield(month, year, yieldResult.value.amount)
        );
      }
    }

    const valueYieldAddendumYear =
      this.props.addendums?.reduce(
        (acc, addendum) =>
          acc + addendum.calculateAnnualProportionalAddendumValue(year),
        0
      ) ?? 0;

    const valueTotal =
      valueYieldAddendumYear +
      yearYields.reduce((acc, current) => acc + current.amount, 0);

    return Number.parseFloat(valueTotal.toFixed(2));
  }

  calculateTotalInvestiment(year: number): number {
    const validAdditives = this.props.addendums?.filter((addendum) =>
      addendum.isAddendumValidForYear(addendum, year)
    );

    const totalInvestimentAddendums =
      validAdditives?.reduce(
        (acc, addendum) => acc + addendum.getValue().amount,
        0
      ) ?? 0;

    const amountInvestiment = this.getInvestimentAmountByYear(year);

    return amountInvestiment + totalInvestimentAddendums;
  }

  private isYearWithinContract(
    year: number,
    startDate: string,
    endDate: string
  ): boolean {
    const startYear = new Date(startDate).getFullYear();
    const endYear = new Date(endDate).getFullYear();

    return year >= startYear && year <= endYear;
  }

  getTotalInvestimentForAddedum(year: number) {
    const validAdditives = this.props.addendums?.filter((addendum) =>
      addendum.isAddendumValidForYear(addendum, year)
    );

    const totalInvestimentAddendums =
      validAdditives?.reduce(
        (acc, addendum) => acc + addendum.getValue().amount,
        0
      ) ?? 0;

    return totalInvestimentAddendums;
  }
  getIncomeReportExistingYear(year: number) {
    return this.props.incomeReport?.find((item) => item.year === year)?.fileUrl;
  }

  getInvestimentAmountByYear(year: number) {
    if (year < this.getContractYears()[0] || year > this.getContractYears()[1])
      return 0;

    return this.props.amount.amount;
  }

  getContractPdf() {
    return this.props.contractPdf;
  }

  getProofPayment() {
    return this.props.proofPayment;
  }

  getPersonalDocument() {
    return this.props.personalDocument;
  }

  getCompanyDocument() {
    return this.props.companyDocument;
  }
  getProofOfResidence() {
    return this.props.proofOfResidence;
  }

  getCardCnpj() {
    return this.props.cardCnpj;
  }

  getContractYears() {
    return [this.getStartDate().getFullYear(), this.getEndDate().getFullYear()];
  }

  addAddendum(addendum: ContractAddendum): void {
    this.props.addendums?.push(addendum);
  }

  getStatus(): ContractStatus {
    return this.status;
  }

  getStartDate(): Date {
    return this.props.startDate;
  }

  getEndDate(): Date {
    return this.props.endDate;
  }

  getInvestor(): Investor {
    return this.props.investor;
  }

  getBroker(): string {
    return this.props.brokerId;
  }

  getAdvisors() {
    return this.props.advisors;
  }

  getProfitability(): Percentage {
    return this.props.profitability;
  }

  getAmount(): Money {
    return this.props.amount;
  }

  getContractType(): ContractType {
    return this.props.type;
  }

  getSignatureRequestId(): string | undefined {
    return this.props.signatureRequestId;
  }

  getProfile(): Profile {
    return this.props.profile;
  }

  getPaymentMethod(): PaymentMethod {
    return this.props.paymentMethod;
  }

  getAddendums(): ContractAddendum[] {
    return this.props.addendums ?? [];
  }

  getContractUrl() {
    return this.props.contractPdf;
  }

  getContractNumber() {
    return this.props.contractNumber;
  }

  getQuotaQuantity() {
    return this.props.quotaQuantity;
  }

  getDurationInMonths(): number | undefined {
    return this.props.durationInMonths;
  }

  markSignatureSent(signatureRequestId: string): void {
    this.props.signatureRequestId = signatureRequestId;
    this.status = ContractStatus.SIGNATURE_SENT;
  }

  markInvestorSignaturePending(): void {
    this.status = ContractStatus.AWAITING_INVESTOR_SIGNATURE;
  }

  markInvestorSigned(): void {
    this.status = ContractStatus.AWAITING_DEPOSIT;
  }

  markAsAuditedAndActive(): void {
    this.status = ContractStatus.ACTIVE;
  }

  markAsRejected(): void {
    this.status = ContractStatus.REJECTED;
  }

  markAsExpired(): void {
    this.status = ContractStatus.EXPIRED;
  }

  markAsAwaitingInvestorSignature(): void {
    this.status = ContractStatus.AWAITING_INVESTOR_SIGNATURE;
  }

  markAsAwaitingAuditSignature(): void {
    this.status = ContractStatus.AWAITING_INVESTOR_SIGNATURE;
  }

  markAsExpiredByAudit(): void {
    this.status = ContractStatus.EXPIRED_BY_AUDIT;
  }

  markAsExpiredFailureProofPayment(): void {
    this.status = ContractStatus.EXPIRED_FAILURE_PROOF_PAYMENT;
  }

  markAsExpiredBySignature(): void {
    this.status = ContractStatus.EXPIRED_BY_INVESTOR;
  }

  markAwaitingDeposit(): void {
    this.status = ContractStatus.AWAITING_DEPOSIT;
  }

  markAsSignatureFailed(): void {
    this.status = ContractStatus.SIGNATURE_FAILED;
  }

  markAsActive() {
    this.status = ContractStatus.ACTIVE;
  }

  markAsFailedToGenerateContract() {
    this.status = ContractStatus.GENERATE_CONTRACT_FAILED;
  }

  markAsAwaitingUpgrade(): void {
    this.status = ContractStatus.AWAITING_UPGRADE;
  }

  markAsInactive(): void {
    this.status = ContractStatus.INACTIVE;
  }

  appendContractUrl(contractUrl: string) {
    this.props.contractPdf = contractUrl;
  }

  addFiles(files: {
    proofPaymentUrl: string;
    contractUrl: string;
    proofResidenceUrl: string;
    personalDocument?: string;
    companyDocument?: string;
  }): void {
    this.props.proofPayment = files.proofPaymentUrl;
    this.props.contractPdf = files.contractUrl;
    this.props.companyDocument = files.companyDocument;
    this.props.proofOfResidence = files.proofResidenceUrl;
    this.props.personalDocument = files.personalDocument;
    this.status = ContractStatus.AWAITING_AUDIT;
    this.touch();
  }

  getProofPaymentUrl(): string | undefined | null {
    return this.props.proofPayment;
  }

  markAwaitingAudit(): void {
    this.status = ContractStatus.AWAITING_AUDIT;
  }

  getIsDebenture(): boolean {
    return this.props.isDebenture;
  }

  editContractData(
    name: string,
    email: string,
    phone: string,
    addressRaw: {
      street: string;
      number: string;
      city: string;
      state: string;
      postalCode: string;
      neighborhood: string;
      complement?: string;
    },
    data: {
      cpf?: string;
      birthDate?: Date;
      motherName?: string;
      occupation?: string;
      nationality?: string;
      rg?: string;
      issuingAgency?: string;
      cnpj?: string;
      companyType?: CompanyType;
      legalRepresentative?: Individual;
    }
  ): Either<Error, void> {
    const updateResult = this.props.investor.update(
      name,
      email,
      phone,
      addressRaw,
      data
    );

    if (updateResult.isLeft()) {
      return left(updateResult.value);
    }

    this.touch();
    return right(undefined);
  }

  resubmitContractData(
    name: string,
    email: string,
    phone: string,
    addressRaw: {
      street: string;
      number: string;
      city: string;
      state: string;
      postalCode: string;
      neighborhood: string;
      complement?: string;
    },
    data: {
      cpf?: string;
      birthDate?: Date;
      motherName?: string;
      occupation?: string;
      nationality?: string;
      rg?: string;
      issuingAgency?: string;
      cnpj?: string;
      companyType?: CompanyType;
      legalRepresentative?: Individual;
      personalDocumentUrl?: string;
      proofOfResidenceUrl?: string;
      cardCnpjUrl?: string;
    }
  ): Either<Error, void> {
    const updateResult = this.props.investor.update(
      name,
      email,
      phone,
      addressRaw,
      data
    );

    if (updateResult.isLeft()) {
      return left(updateResult.value);
    }

    if (data.personalDocumentUrl) {
      this.props.personalDocument = data.personalDocumentUrl;
    }

    if (data.proofOfResidenceUrl) {
      this.props.proofOfResidence = data.proofOfResidenceUrl;
    }

    if (data.cardCnpjUrl) {
      this.props.cardCnpj = data.cardCnpjUrl;
    }

    this.status = ContractStatus.AWAITING_AUDIT;
    this.touch();
    return right(undefined);
  }

  updateContractDetails(
    type: ContractType,
    startDate: Date,
    endDate: Date,
    isDebenture: boolean,
    amount?: Money,
    monthlyRate?: Percentage,
    paymentMethod?: PaymentMethod,
    profile?: Profile,
    proofPayment?: string,
    contractUrl?: string,
    quotaQuantity?: number,
    durationInMonths?: number
  ): Either<Error, void> {
    if (endDate <= startDate) {
      return left(new Error("Data final deve ser posterior à data inicial"));
    }

    this.props.type = type;
    this.props.startDate = startDate;
    this.props.endDate = endDate;
    this.props.isDebenture = isDebenture;

    if (amount) {
      this.props.amount = amount;
    }

    if (monthlyRate) {
      this.props.profitability = monthlyRate;
    }

    if (paymentMethod) {
      this.props.paymentMethod = paymentMethod;
    }

    if (profile) {
      this.props.profile = profile;
    }

    if (proofPayment) {
      this.props.proofPayment = proofPayment;
    }

    if (contractUrl) {
      this.props.contractPdf = contractUrl;
    }

    if (quotaQuantity) {
      this.props.quotaQuantity = quotaQuantity;
    }

    if (durationInMonths) {
      this.props.durationInMonths = durationInMonths;
    }

    this.touch();
    return right(undefined);
  }

  updateContractStatus(status: ContractStatus): Either<Error, void> {
    this.status = status;
    this.touch();
    return right(undefined);
  }
}
