import type { ISignatureAPI } from '@/application/interfaces/signature-api'
import type { ContractPdfGeneratedEvent } from '@/domain/events/contracts/contract-pdf-generated.event'
import { ContractSignatureFailedEvent } from '@/domain/events/contracts/contract-signature-failed.event'
import { ContractSignatureSentEvent } from '@/domain/events/contracts/contract-signature-sent.event'
import type { LoggerGateway } from '@/domain/gateways/logger.gateway'
import type { IInvestmentContractRepository } from '@/domain/repositories'
import { DomainEventMediator } from '@/domain/shared'

export class SendContractToSignatureHandler {
  constructor(
    private readonly repo: IInvestmentContractRepository,
    private readonly signatureApi: ISignatureAPI,
    private readonly logger: LoggerGateway
  ) {}

  async handle(event: ContractPdfGeneratedEvent): Promise<void> {
    this.logger.info(
      `Iniciando o processamento do evento ContractPdfGeneratedEvent para o contrato com ID: ${event.payload.contract.id}`
    )

    const contract = event.payload.contract
    this.logger.info(
      `Contrato encontrado. ID: ${contract.id}. Iniciando envio para assinatura.`
    )

    try {
      const response = await this.signatureApi.send(
        contract,
        event.payload.contractFile
      )

      if (response.isLeft()) {
        this.logger.error(
          `Erro no envio para assinatura do contrato com ID: ${contract.id}. Mensagem: ${response.value.message}`
        )
        contract.markAsSignatureFailed()
        await this.repo.update(contract.id, contract)

        // Verificar se precisa fazer rollback de upgrade
        await this.handleUpgradeRollback(contract)

        DomainEventMediator.dispatch(
          new ContractSignatureFailedEvent(contract.id, response.value.message)
        )
        this.logger.info(
          `Evento ContractSignatureFailedEvent despachado para o contrato com ID: ${contract.id}`
        )
        return
      }

      this.logger.info(
        `Assinatura enviada com sucesso para o contrato com ID: ${contract.id}. Request ID: ${response.value.requestId}`
      )
      contract.markSignatureSent(response.value.requestId)
      contract.markInvestorSignaturePending()
      await this.repo.update(contract.id, contract)
      this.logger.info(
        `Contrato atualizado com status de assinatura enviada para o contrato com ID: ${contract.id}`
      )

      DomainEventMediator.dispatch(
        new ContractSignatureSentEvent(contract.id, response.value.requestId)
      )
      this.logger.info(
        `Evento ContractSignatureSentEvent despachado para o contrato com ID: ${contract.id}`
      )
    } catch (error) {
      contract.markAsSignatureFailed()
      await this.repo.update(contract.id, contract)

      // Verificar se precisa fazer rollback de upgrade
      await this.handleUpgradeRollback(contract)
    }
  }

  /**
   * Verifica se o contrato com falha era um upgrade e reverte o contrato original para ACTIVE
   */
  private async handleUpgradeRollback(failedContract: InvestmentContract): Promise<void> {
    try {
      // Buscar contratos do mesmo investidor com status AWAITING_UPGRADE
      const originalContractsResult = await this.repo.findByInvestorAndStatus(
        failedContract.getInvestor().id,
        ContractStatus.AWAITING_UPGRADE
      )

      if (originalContractsResult.isLeft()) {
        this.logger.error(
          `Erro ao buscar contratos originais para rollback: ${originalContractsResult.value.message}`
        )
        return
      }

      const originalContracts = originalContractsResult.value

      if (originalContracts.length > 0) {
        this.logger.info(
          `Encontrados ${originalContracts.length} contratos aguardando upgrade para rollback após falha do contrato ${failedContract.id}`
        )

        // Reverter todos os contratos originais para ACTIVE
        for (const originalContract of originalContracts) {
          originalContract.markAsActive()
          await this.repo.update(originalContract.id, originalContract)

          this.logger.info(
            `Contrato original ${originalContract.id} revertido para ACTIVE após falha do upgrade ${failedContract.id}`
          )
        }
      }
    } catch (error) {
      this.logger.error(
        `Erro inesperado ao processar rollback de upgrade: ${error}`
      )
    }
  }
}
