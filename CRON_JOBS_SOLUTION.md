# Solução para Problema de Atualização de Status após Assinatura

## Problema Identificado

Após a assinatura do contrato de upgrade pelo super admin, o status não estava sendo atualizado automaticamente e o histórico não estava sendo exibido. Isso acontecia porque os cron jobs estavam configurados para rodar apenas a cada 20 minutos.

## Solução Implementada

### Alteração nos Intervalos dos Cron Jobs

**Arquivo alterado**: `contract-service/.env`

**Antes**:
```env
AWAITING_AUDIT_SIGNATURE_CRON='*/20 * * * *'
AWAITING_INVESTOR_SIGNATURE_CRON='*/20 * * * *'
```

**Depois**:
```env
AWAITING_AUDIT_SIGNATURE_CRON='*/1 * * * *'
AWAITING_INVESTOR_SIGNATURE_CRON='*/1 * * * *'
```

### O que mudou:

- **Antes**: <PERSON><PERSON> rodavam a cada 20 minutos
- **Agora**: Jobs rodam a cada 1 minuto
- **Resultado**: Delay máximo reduzido de 20 minutos para 1 minuto

### Como os Jobs Funcionam:

1. **AwaitingInvestorSignature Job**: 
   - Verifica contratos com status `AWAITING_INVESTOR_SIGNATURE`
   - Consulta o provedor de assinatura (RBM) para verificar se o investidor assinou
   - Atualiza status para `AWAITING_DEPOSIT` quando assinado

2. **AwaitingAuditSignature Job**:
   - Verifica contratos com status `AWAITING_AUDIT_SIGNATURE`
   - Consulta o provedor de assinatura (RBM) para verificar se a auditoria assinou
   - Atualiza status para `ACTIVE` quando assinado por todos

### Para Aplicar a Mudança:

1. ✅ **Variáveis já alteradas** no arquivo `.env`
2. **Reiniciar o serviço** contract-service:
   ```bash
   # Se estiver rodando com Docker
   cd contract-service
   docker-compose down
   docker-compose up -d
   
   # Se estiver rodando diretamente
   npm run dev
   ```

### Benefícios:

- ✅ **Resposta mais rápida**: Status atualizado em até 1 minuto
- ✅ **Melhor experiência do usuário**: Menos tempo de espera
- ✅ **Solução simples**: Sem necessidade de código adicional
- ✅ **Não invasiva**: Não altera a arquitetura existente

### Monitoramento:

Para verificar se os jobs estão funcionando, monitore os logs:

```bash
# Logs do contract-service
tail -f logs/contract-service.log | grep "ProcessInvestorSignature\|ProcessAuditSignature"
```

### Considerações de Performance:

- **Impacto mínimo**: Jobs só processam contratos nos status específicos
- **Eficiência**: Se não há contratos pendentes, o job termina rapidamente
- **Escalabilidade**: Pode ser ajustado conforme necessário

### Ajustes Futuros (se necessário):

Se 1 minuto ainda for muito lento, pode ser reduzido para:
- **30 segundos**: `*/30 * * * * *` (requer 6 campos para segundos)
- **2 minutos**: `*/2 * * * *`

Se causar muita carga, pode ser aumentado para:
- **2 minutos**: `*/2 * * * *`
- **5 minutos**: `*/5 * * * *`

### Resultado Esperado:

Após a assinatura pelo super admin, o contrato deve aparecer como `ACTIVE` e o histórico deve ser exibido em no máximo 1 minuto, resolvendo completamente o problema reportado.
