# Fix: Perfil de broker não lista contratos após assinatura do investidor

## Problema Identificado

O perfil do broker não estava listando novos contratos imediatamente após a assinatura do investidor devido a um delay no processo de verificação de assinaturas.

### Causa Raiz

O sistema utiliza cron jobs para verificar o status de assinaturas:
- **Assinatura do Investidor**: Verificada a cada 5 minutos (`*/5 * * * *`)
- **Assinatura da Auditoria**: Verificada a cada 5 minutos (`*/5 * * * *`)

Isso significa que pode haver um delay de até **10 minutos** entre:
1. Investidor assinar o contrato
2. Sistema detectar a assinatura e mudar status para `AWAITING_DEPOSIT`
3. Comprovante ser enviado e status mudar para `AWAITING_AUDIT_SIGNATURE`
4. Auditoria assinar e status mudar para `ACTIVE`
5. Contrato aparecer na listagem do broker

## Solução Implementada

### 1. Novo Endpoint no Contract Service

**Arquivo**: `contract-service/src/presentation/http/controllers/contracts/force-signature-check.controller.ts`

Endpoint que força a verificação imediata do status de assinatura:
```
POST /contracts/:contractId/force-signature-check
```

### 2. Serviço no ICA Contracts Backend

**Arquivo**: `ica-contracts-backend/src/apis/ica-contract-service/services/force-signature-check.service.ts`

Serviço que chama o endpoint do contract-service.

### 3. Endpoint no ICA Contracts Backend

**Arquivo**: `ica-contracts-backend/src/modules/contract/controller/contract.controller.ts`

Endpoint público para o frontend:
```
POST /contract/:id/force-signature-check
```

### 4. Hook React no Frontend

**Arquivo**: `ica-invest-contracts/src/hooks/useForceSignatureCheck.ts`

Hook para facilitar o uso no frontend.

### 5. Componente de Botão

**Arquivo**: `ica-invest-contracts/src/components/ForceSignatureCheckButton/index.tsx`

Componente reutilizável que pode ser adicionado em qualquer lugar do frontend.

## Como Usar

### ✅ Implementado nas Listagens

O botão já foi adicionado automaticamente nas seguintes telas:

1. **Meus Contratos** (`/meus-contratos`) - Nova coluna "Ações"
2. **Contratos** (`/contratos`) - Nova coluna "Ações"

O botão aparece apenas para contratos com status:
- `AWAITING_INVESTOR_SIGNATURE`
- `AWAITING_DEPOSIT`
- `AWAITING_AUDIT_SIGNATURE`

### No Frontend (React) - Uso Manual

```tsx
import { ForceSignatureCheckButton } from '@/components/ForceSignatureCheckButton';

function ContractCard({ contract }) {
  return (
    <div>
      <h3>{contract.name}</h3>
      <p>Status: {contract.status}</p>

      {contract.status !== 'ACTIVE' && (
        <ForceSignatureCheckButton
          contractId={contract.id}
          onSuccess={(newStatus) => {
            console.log('Novo status:', newStatus);
            // Atualizar UI conforme necessário
          }}
        />
      )}
    </div>
  );
}
```

### Diretamente via API

```bash
# Verificar status de assinatura de um contrato específico
curl -X POST \
  http://localhost:3000/contract/{contractId}/force-signature-check \
  -H "Authorization: Bearer {token}"
```

## Benefícios

1. **Verificação Imediata**: Não precisa esperar até 10 minutos pelo cron job
2. **Melhor UX**: Brokers podem forçar atualização quando necessário
3. **Transparência**: Feedback imediato sobre o status da assinatura
4. **Flexibilidade**: Pode ser usado em qualquer parte do sistema

## Arquivos Modificados/Criados

### Contract Service
- `src/presentation/http/controllers/contracts/force-signature-check.controller.ts` (novo)
- `src/main/factories/controllers/contracts/force-signature-check-controller.factory.ts` (novo)
- `src/main/routes/contracts/contract.route.ts` (modificado)

### ICA Contracts Backend
- `src/apis/ica-contract-service/services/force-signature-check.service.ts` (novo)
- `src/apis/ica-contract-service/ica-contract-service.module.ts` (modificado)
- `src/modules/contract/controller/contract.controller.ts` (modificado)

### Frontend
- `src/services/force-signature-check.service.ts` (novo)
- `src/hooks/useForceSignatureCheck.ts` (novo)
- `src/components/ForceSignatureCheckButton/index.tsx` (novo)
- `src/app/meus-contratos/_screen/index.tsx` (modificado - adicionada coluna "Ações")
- `src/app/contratos/_screen/index.tsx` (modificado - adicionada coluna "Ações")

## Próximos Passos

1. **Testar** a implementação em ambiente de desenvolvimento
2. **Adicionar o botão** nas telas relevantes (dashboard do broker, listagem de contratos)
3. **Considerar** adicionar verificação automática após ações do usuário (ex: após envio de comprovante)
4. **Monitorar** logs para identificar possíveis problemas
5. **Documentar** para a equipe como usar a nova funcionalidade
