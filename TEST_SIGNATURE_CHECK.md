# Teste da Funcionalidade de Verificação de Assinatura

## Como Testar

### 1. Verificar se os serviços estão rodando

```bash
# Contract Service
cd contract-service
npm run dev

# ICA Contracts Backend  
cd ica-contracts-backend
npm run start:dev

# Frontend
cd ica-invest-contracts
npm run dev
```

### 2. Testar o endpoint diretamente

```bash
# Substituir {contractId} pelo ID de um contrato real
curl -X POST \
  http://localhost:3000/contract/{contractId}/force-signature-check \
  -H "Authorization: Bearer {seu-token}" \
  -H "Content-Type: application/json"
```

**Resposta esperada:**
```json
{
  "contractId": "123",
  "status": "ACTIVE",
  "message": "Signature status checked and updated successfully"
}
```

### 3. Testar no Frontend

1. **Acesse a listagem de contratos:**
   - `/contratos` (para brokers/admins)
   - `/meus-contratos` (para investidores)

2. **Procure por contratos com status:**
   - `AWAITING_INVESTOR_SIGNATURE`
   - `AWAITING_DEPOSIT`
   - `AWAITING_AUDIT_SIGNATURE`

3. **Clique no botão "Verificar"** na coluna "Ações"

4. **Observe:**
   - Loading state no botão
   - Atualização automática da lista após sucesso
   - Mensagens de erro se houver problemas

### 4. Cenários de Teste

#### Cenário 1: Contrato Aguardando Assinatura do Investidor
- Status inicial: `AWAITING_INVESTOR_SIGNATURE`
- Ação: Investidor assina no sistema externo
- Resultado esperado: Status muda para `AWAITING_DEPOSIT`

#### Cenário 2: Contrato Aguardando Comprovante
- Status inicial: `AWAITING_DEPOSIT`
- Ação: Comprovante é enviado e auditoria assina
- Resultado esperado: Status muda para `ACTIVE`

#### Cenário 3: Contrato Aguardando Assinatura da Auditoria
- Status inicial: `AWAITING_AUDIT_SIGNATURE`
- Ação: Auditoria assina no sistema externo
- Resultado esperado: Status muda para `ACTIVE`

### 5. Logs para Monitorar

#### Contract Service
```bash
# Verificar logs do processamento de assinaturas
tail -f logs/contract-service.log | grep "ProcessInvestorSignature\|ProcessAuditSignature"
```

#### ICA Contracts Backend
```bash
# Verificar logs da API
tail -f logs/ica-contracts-backend.log | grep "ForceSignatureCheck"
```

### 6. Troubleshooting

#### Problema: Botão não aparece
- **Verificar:** Se o contrato tem um dos status esperados
- **Solução:** Confirmar que o status está correto no banco

#### Problema: Erro 404 no endpoint
- **Verificar:** Se o contract-service está rodando
- **Verificar:** Se a variável `API_CONTRACT_SERVICE_URL` está configurada
- **Solução:** Configurar corretamente as variáveis de ambiente

#### Problema: Erro de autorização
- **Verificar:** Se o token JWT está válido
- **Verificar:** Se o usuário tem permissão (BROKER, ADVISOR, SUPERADMIN)
- **Solução:** Fazer login novamente ou verificar roles

#### Problema: Status não atualiza
- **Verificar:** Se o contrato realmente foi assinado no sistema externo
- **Verificar:** Logs do contract-service para erros
- **Solução:** Verificar configuração do sistema de assinaturas

### 7. Variáveis de Ambiente Necessárias

#### ICA Contracts Backend
```env
API_CONTRACT_SERVICE_URL=http://localhost:3001
```

#### Contract Service
```env
# Configurações do sistema de assinaturas
SIGNATURE_SERVICE_URL=...
SIGNATURE_API_KEY=...
```

### 8. Monitoramento de Performance

- **Tempo de resposta:** Deve ser < 5 segundos
- **Taxa de sucesso:** > 95%
- **Logs de erro:** Monitorar para identificar problemas

### 9. Rollback (se necessário)

Se houver problemas, remover:

1. **Frontend:**
   - Coluna "Ações" das tabelas
   - Componente ForceSignatureCheckButton
   - Hook useForceSignatureCheck
   - Serviço force-signature-check.service

2. **Backend:**
   - Endpoint `/contract/:id/force-signature-check`
   - Serviço ForceSignatureCheckService

3. **Contract Service:**
   - Endpoint `/contracts/:contractId/force-signature-check`
   - Controller ForceSignatureCheckController

O sistema continuará funcionando normalmente com os cron jobs de 5 minutos.
