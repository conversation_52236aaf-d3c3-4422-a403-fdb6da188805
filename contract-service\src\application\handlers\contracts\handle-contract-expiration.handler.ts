import { ContractStatus } from '@/domain/entities/contracts'
import type { ContractExpiredEvent } from '@/domain/events/contracts/contract-expired.event'
import type { IInvestmentContractRepository } from '@/domain/repositories'

export class HandleContractExpiration {
  constructor(private readonly repo: IInvestmentContractRepository) {}

  async handle(event: ContractExpiredEvent): Promise<void> {
    const contract = await this.repo.findById(event.payload.contractId)

    if (contract.isLeft()) {
      return
    }

    if (!contract.value) {
      console.warn(`[WARN] Contract not found: ${event.payload.contractId}`)
      return
    }

    const currentStatus = contract.value.getStatus()

    // Só atualiza se estiver em um estado válido para expiração
    if (currentStatus === ContractStatus.AWAITING_INVESTOR_SIGNATURE) {
      contract.value.markAsExpired()
      await this.repo.save(contract.value)
      console.log(`[INFO] Contract ${contract.value.id} marked as EXPIRED`)

      // Verificar se precisa fazer rollback de upgrade
      await this.handleUpgradeRollback(contract.value)
    } else {
      console.log(
        `[SKIP] Contract ${contract.value.id} is not in a status to be expired. Current: ${currentStatus}`
      )
    }
  }

  /**
   * Verifica se o contrato expirado era um upgrade e reverte o contrato original para ACTIVE
   */
  private async handleUpgradeRollback(expiredContract: InvestmentContract): Promise<void> {
    try {
      // Buscar contratos do mesmo investidor com status AWAITING_UPGRADE
      const originalContractsResult = await this.repo.findByInvestorAndStatus(
        expiredContract.getInvestor().id,
        ContractStatus.AWAITING_UPGRADE
      )

      if (originalContractsResult.isLeft()) {
        console.error(
          `[ERROR] Erro ao buscar contratos originais para rollback: ${originalContractsResult.value.message}`
        )
        return
      }

      const originalContracts = originalContractsResult.value

      if (originalContracts.length > 0) {
        console.log(
          `[INFO] Encontrados ${originalContracts.length} contratos aguardando upgrade para rollback após expiração do contrato ${expiredContract.id}`
        )

        // Reverter todos os contratos originais para ACTIVE
        for (const originalContract of originalContracts) {
          originalContract.markAsActive()
          await this.repo.save(originalContract)

          console.log(
            `[INFO] Contrato original ${originalContract.id} revertido para ACTIVE após expiração do upgrade ${expiredContract.id}`
          )
        }
      }
    } catch (error) {
      console.error(
        `[ERROR] Erro inesperado ao processar rollback de upgrade: ${error}`
      )
    }
  }
}
