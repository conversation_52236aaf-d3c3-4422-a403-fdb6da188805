import type { ITransactionContext } from "@/application/interfaces";
import type {
  ContractStatus,
  InvestmentContract,
} from "@/domain/entities/contracts";
import { Company, Individual } from "@/domain/entities/parties";
import { IncomeReportEmail } from "@/domain/entities/reports/income-report-email.entity";
import type {
  ContractFilters,
  IInvestmentContractRepository,
} from "@/domain/repositories";
import { type Either, PaginatedResult, left, right } from "@/domain/shared";
import { ContractStatusMapper } from "@/domain/shared/contract-status-mapper";
import { PinoLoggerAdapter } from "@/main/adapters/pino-logger.adapter";
import type { PrismaClient } from "@prisma/client";
import prisma from "../client";
import { InvestmentContractMapper } from "../mappers";
import { hasGetClient } from "./broker.repository";

type DbClient =
  | PrismaClient
  | Omit<
      PrismaClient,
      "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends"
    >;

export class PrismaInvestmentContractRepository
  implements IInvestmentContractRepository
{
  private readonly logger = new PinoLoggerAdapter();

  async resubmitRejectedContract(
    contractId: string,
    data: InvestmentContract,
    tx?: ITransactionContext
  ): Promise<Either<Error, void>> {
    try {
      const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

      const investor = data.getInvestor();

      const party = investor.getParty();

      this.logger.info(`Iniciando atualização de contrato ${contractId}`);

      const existingContract = await client.contract.findUnique({
        where: { id: contractId },
        include: {
          pre_register: true,
          owner_role_relation_contract_investor_idToowner_role_relation: {
            include: {
              owner: { include: { address: true } },
              business: { include: { address: true } },
            },
          },
        },
      });

      if (!existingContract) {
        return left(new Error("Contrato não encontrado"));
      }

      const ownerRelation =
        existingContract.owner_role_relation_contract_investor_idToowner_role_relation;

      const mainAddress = data.getInvestor().getParty().getAddress();
      const mainAddressData = {
        street: mainAddress.street,
        number: mainAddress.number,
        neighborhood: mainAddress.neighborhood,
        city: mainAddress.city,
        state: mainAddress.state,
        cep: mainAddress.postalCode,
        complement: mainAddress.complement || null,
      };

      if (party instanceof Individual) {
        const ownerId = ownerRelation?.owner_id;
        if (ownerId) {
          const existingAddress = await client.address.findFirst({
            where: { owner_id: ownerId },
          });
          if (existingAddress) {
            await client.address.update({
              where: { id: existingAddress.id },
              data: mainAddressData,
            });
            this.logger.info(
              `Endereço do Investidor PF atualizado: ${ownerId}`
            );
          } else {
            // Considerar se a criação é necessária ou se deve logar um erro
            this.logger.info(
              `Endereço não encontrado para Investidor PF: ${ownerId}, criação não implementada neste fluxo.`
            );
          }
        }
      } else if (party instanceof Company) {
        const businessId = ownerRelation?.business_id;
        if (businessId) {
          const existingAddress = await client.address.findFirst({
            where: { business_id: businessId },
          });
          if (existingAddress) {
            await client.address.update({
              where: { id: existingAddress.id },
              data: mainAddressData,
            });
            this.logger.info(`Endereço da Empresa atualizado: ${businessId}`);
          } else {
            // Considerar se a criação é necessária ou se deve logar um erro
            this.logger.info(
              `Endereço não encontrado para Empresa: ${businessId}, criação não implementada neste fluxo.`
            );
          }

          // --- INÍCIO: Atualização do endereço do Representante Legal ---
          const representative = party.getLegalRepresentative();
          const representativeAddress = representative.getAddress();
          const representativeOwnerId = ownerRelation?.business?.owner_id; // ID do owner (representante)

          if (representativeOwnerId) {
            const representativeAddressData = {
              street: representativeAddress.street,
              number: representativeAddress.number,
              neighborhood: representativeAddress.neighborhood,
              city: representativeAddress.city,
              state: representativeAddress.state,
              cep: representativeAddress.postalCode,
              complement: representativeAddress.complement || null,
            };
            const existingRepAddress = await client.address.findFirst({
              where: { owner_id: representativeOwnerId },
            });
            if (existingRepAddress) {
              await client.address.update({
                where: { id: existingRepAddress.id },
                data: representativeAddressData,
              });
              this.logger.info(
                `Endereço do Representante Legal atualizado: ${representativeOwnerId}`
              );
            } else {
              this.logger.info(
                `Endereço não encontrado para Representante Legal: ${representativeOwnerId}, atualização ignorada.`
              );
            }
          } else {
            this.logger.error(
              `ID do Representante Legal não encontrado no relacionamento para atualizar endereço. Business ID: ${businessId}`
            );
          }
        }
      }

      if (
        existingContract
          .owner_role_relation_contract_investor_idToowner_role_relation
          ?.business &&
        party instanceof Company
      ) {
        await client.business.update({
          where: {
            id:
              existingContract
                .owner_role_relation_contract_investor_idToowner_role_relation
                ?.business?.id ?? "",
          },
          data: {
            fantasy_name: party.getName(),
            company_name: party.getName(),
            cnpj: investor.getParty().getDocument(),
            type: party.getCompanyType().toString(),
          },
        });

        await client.owner.update({
          where: {
            id:
              existingContract
                .owner_role_relation_contract_investor_idToowner_role_relation
                ?.business?.owner_id ?? "",
          },
          data: {
            name: party.getLegalRepresentative().getName(),
            email: party.getLegalRepresentative().getEmail(),
            phone: party.getLegalRepresentative().getPhone(),
            cpf: party.getLegalRepresentative().getDocument(),
            rg: party.getLegalRepresentative().getRg(),
            dt_birth: party.getLegalRepresentative().getBirthDate(),
            mother_name: party.getLegalRepresentative().getMotherName(),
            occupation: party.getLegalRepresentative().getOccupation(),
            issuingAgency: party.getLegalRepresentative().getIssuingAgency?.(),
            nationality: party.getLegalRepresentative().getNationality?.(),
          },
        });
      }

      if (
        existingContract
          .owner_role_relation_contract_investor_idToowner_role_relation
          ?.owner &&
        party instanceof Individual
      ) {
        await client.owner.update({
          where: {
            id:
              existingContract
                .owner_role_relation_contract_investor_idToowner_role_relation
                ?.owner?.id ?? "",
          },
          data: {
            name: party.getName(),
            email: party.getEmail(),
            phone: party.getPhone(),
            cpf: party.getDocument(),
            rg: party.getRg(),
            dt_birth: party.getBirthDate(),
            mother_name: party.getMotherName(),
            occupation: party.getOccupation(),
            issuingAgency: party.getIssuingAgency?.(),
            nationality: party.getNationality?.(),
          },
        });
      }

      // Upsert personal document in uploads table
      const personalDocumentUrl = data.getPersonalDocument();
      if (personalDocumentUrl) {
        let ownerId: string | undefined | null;

        if (party instanceof Individual) {
          ownerId =
            existingContract
              .owner_role_relation_contract_investor_idToowner_role_relation
              ?.owner?.id;
        } else if (party instanceof Company) {
          ownerId =
            existingContract
              .owner_role_relation_contract_investor_idToowner_role_relation
              ?.business?.owner_id; // ID do representante legal
        }

        if (!ownerId) {
          this.logger.error(
            `Owner ID (representante legal ou PF) não encontrado para upsert do documento pessoal. Contract ID: ${contractId}`
          );
        } else {
          const existingUpload = await client.uploads.findFirst({
            where: {
              owner_id: ownerId,
              type: "RG", // Mantendo 'RG' conforme o código original desta função
            },
          });

          if (existingUpload) {
            await client.uploads.update({
              where: { id: existingUpload.id },
              data: {
                url: personalDocumentUrl,
                updated_at: new Date(),
              },
            });
          } else {
            await client.uploads.create({
              data: {
                type: "RG", // Mantendo 'RG'
                url: personalDocumentUrl,
                owner_id: ownerId,
              },
            });
          }
        }
      }

      // Upsert proof of residence in uploads table
      const proofOfResidenceUrl = data.getProofOfResidence();
      if (proofOfResidenceUrl) {
        let ownerId: string | undefined | null;

        if (party instanceof Individual) {
          ownerId =
            existingContract
              .owner_role_relation_contract_investor_idToowner_role_relation
              ?.owner?.id;
        } else if (party instanceof Company) {
          ownerId =
            existingContract
              .owner_role_relation_contract_investor_idToowner_role_relation
              ?.business?.owner_id || undefined; // ID do representante legal
        }

        if (!ownerId) {
          this.logger.info(
            `Owner ID (representante legal ou PF) não encontrado para upsert do comprovante de residência. Contract ID: ${contractId}`
          );
        } else {
          const existingProofUpload = await client.uploads.findFirst({
            where: {
              owner_id: ownerId,
              type: "PROOF_OF_RESIDENCE",
            },
          });

          if (existingProofUpload) {
            await client.uploads.update({
              where: { id: existingProofUpload.id },
              data: {
                url: proofOfResidenceUrl,
                updated_at: new Date(),
              },
            });
          } else {
            await client.uploads.create({
              data: {
                type: "PROOF_OF_RESIDENCE",
                url: proofOfResidenceUrl,
                owner_id: ownerId,
              },
            });
          }
        }
      }

      const cardCnpjUrl = data.getCardCnpj();
      if (cardCnpjUrl && party instanceof Company) {
        const businessId =
          existingContract
            .owner_role_relation_contract_investor_idToowner_role_relation
            ?.business?.id;

        if (!businessId) {
          this.logger.info(
            `Business ID não encontrado para upsert do cartão CNPJ. Contract ID: ${contractId}`
          );
        } else {
          const existingCardCnpjUpload = await client.uploads.findFirst({
            where: {
              business_id: businessId,
              type: "CARD_CNPJ",
            },
          });

          if (existingCardCnpjUpload) {
            await client.uploads.update({
              where: { id: existingCardCnpjUpload.id },
              data: {
                url: cardCnpjUrl,
                updated_at: new Date(),
              },
            });
          } else {
            await client.uploads.create({
              data: {
                type: "CARD_CNPJ",
                url: cardCnpjUrl,
                business_id: businessId,
              },
            });
          }
        }
      }

      await client.contract.update({
        where: { id: contractId },
        data: {
          status: data.getStatus(),
          external_id: data.getSignatureRequestId()
            ? Number(data.getSignatureRequestId())
            : null,
          contract_pdf: data.getContractUrl(),
          proof_payment: data.getProofPayment(),
          type: data.getContractType().valueAsString,
          is_debenture: data.getIsDebenture(),
          start_contract: data.getStartDate(),
          end_contract: data.getEndDate(),
          duration_in_months: data.getDurationInMonths(),
        },
      });

      const { accountNumber, agency, bank, pixKey } = investor.getAccount();

      const preRegisterUpdateData = {
        investment_value: data.getAmount().amount,
        investment_yield: data.getProfitability().valueAsPercent,
        email: investor.getEmail(),
        name: investor.getName(),
        phone_number: investor.getPhone(),
        document: investor.getParty().getDocument(),
        mother_name: investor.getParty().getMotherName(),
        status: "ACTIVE",
        account: accountNumber,
        bank,
        agency,
        pix: pixKey,
        rg: investor.isIndividual()
          ? (investor.getParty() as Individual).getRg()
          : undefined,
        dt_birth: investor.isIndividual()
          ? (investor.getParty() as Individual).getBirthDate()
          : undefined,
        purchase_with: data.getPaymentMethod().value,
        amount_quotes: data.getQuotaQuantity(),
      };

      await client.pre_register.update({
        where: { id: existingContract.pre_register[0].id },
        data: preRegisterUpdateData,
      });

      return right(undefined);
    } catch (error) {
      this.logger.error(
        `Erro ao atualizar contrato. contractId: ${contractId}. Detalhes: ${error}`
      );
      return left(error as Error);
    }
  }

  async update(
    contractId: string,
    data: InvestmentContract,
    tx?: ITransactionContext
  ): Promise<Either<Error, void>> {
    try {
      const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

      const existingContract = await client.contract.findUnique({
        where: { id: contractId },
        include: {
          owner_role_relation_contract_investor_idToowner_role_relation: {
            include: {
              owner: true,
              business: true,
            },
          },
        },
      });

      if (!existingContract) {
        this.logger.error(
          `Contract not found for update. contractId: ${contractId}`
        );
        return left(new Error("Contract not found"));
      }

      await client.contract.update({
        where: { id: contractId },
        data: {
          status: data.getStatus(),
          external_id: data.getSignatureRequestId()
            ? Number(data.getSignatureRequestId())
            : null,
          contract_pdf: data.getContractUrl() ? data.getContractUrl() : null,
        },
      });

      const contractProofPayment = data.getProofPayment();

      if (contractProofPayment) {
        await client.contract.update({
          where: { id: contractId },
          data: { proof_payment: contractProofPayment },
        });
      }

      const personalDocument = data.getPersonalDocument();
      const companyDocument = data.getCompanyDocument();
      const proofOfResidence = data.getProofOfResidence();
      const party = data.getInvestor().getParty();
      const ownerRelation =
        existingContract.owner_role_relation_contract_investor_idToowner_role_relation;

      if (companyDocument) {
        const existingUpload = await client.uploads.findFirst({
          where: {
            business: {
              cnpj: data.getInvestor().getCnpj(),
            },
            type: "COMPANY_DOCUMENT",
          },
        });

        if (existingUpload) {
          await client.uploads.update({
            where: { id: existingUpload.id },
            data: { url: companyDocument },
          });
        }

        if (!existingUpload) {
          await client.uploads.create({
            data: {
              type: "COMPANY_DOCUMENT",
              url: companyDocument,
              business_id: data.getInvestor().getParty().id,
            },
          });
        }
      }

      if (proofOfResidence) {
        let ownerId: string | undefined;
        if (party instanceof Individual) {
          ownerId = ownerRelation?.owner?.id;
        } else if (party instanceof Company) {
          ownerId = ownerRelation?.business?.owner_id || undefined;
        }
        if (!ownerId) {
          this.logger.error(
            "Owner ID not found for proof of residence upload."
          );
        } else {
          const existingUpload = await client.uploads.findFirst({
            where: {
              owner_id: ownerId,
              type: "PROOF_OF_RESIDENCE",
            },
          });

          if (existingUpload) {
            await client.uploads.update({
              where: { id: existingUpload.id },
              data: { url: proofOfResidence },
            });
          }

          if (!existingUpload) {
            await client.uploads.create({
              data: {
                type: "PROOF_OF_RESIDENCE",
                url: proofOfResidence,
                owner_id: ownerId,
              },
            });
          }
        }
      }

      if (personalDocument) {
        let ownerId: string | undefined;
        if (party instanceof Individual) {
          ownerId = ownerRelation?.owner?.id;
        } else if (party instanceof Company) {
          ownerId = ownerRelation?.business?.owner_id || undefined;
        }
        if (!ownerId) {
          this.logger.error("Owner ID not found for personal document upload.");
        } else {
          const existingUpload = await client.uploads.findFirst({
            where: {
              owner_id: ownerId,
              type: "RG",
            },
          });

          if (existingUpload) {
            await client.uploads.update({
              where: { id: existingUpload.id },
              data: { url: personalDocument },
            });
          }

          if (!existingUpload) {
            await client.uploads.create({
              data: {
                type: "RG",
                url: personalDocument,
                owner_id: ownerId,
              },
            });
          }
        }
      }

      return right(undefined);
    } catch (error) {
      this.logger.error(
        `Erro ao salvar contrato. contractId: ${contractId}. Detalhes: ${error}`
      );
      return left(error as Error);
    }
  }

  async findPaginated(
    userId: string,
    page: number,
    limit: number
  ): Promise<Either<Error, PaginatedResult<InvestmentContract>>> {
    this.logger.info(
      `Iniciando busca paginada de contratos para usuário ID: ${userId} (page: ${page}, limit: ${limit})`
    );
    try {
      const user = await prisma.owner_role_relation.findFirst({
        where: { id: userId },
        include: {
          role: true,
        },
      });

      if (!user) {
        this.logger.error(`Usuário não encontrado para ID: ${userId}`);
        return left(new Error("Usuário não encontrado"));
      }

      const {
        role: { name: roleName },
      } = user;
      this.logger.info(`Usuário encontrado com role: ${roleName}`);

      const skip = (page - 1) * limit;
      let whereCondition = {};

      if (roleName === "investor") {
        whereCondition = { investor_id: userId };
      }
      if (roleName === "broker") {
        whereCondition = { owner_role_relation: userId };
      }
      if (roleName === "advisor") {
        whereCondition = { contract_advisor: { some: { id_advisor: userId } } };
      }
      if (roleName === "admin") {
        const brokersManaged = await prisma.wallets_views.findMany({
          where: { upper_id: userId },
          select: { bottom_id: true },
        });
        const brokerIds = brokersManaged.map(
          (b: { bottom_id: string }) => b.bottom_id
        );
        whereCondition = { owner_role_relation: { in: brokerIds } };
      }

      // Condição adicional conforme lógica do negócio
      whereCondition = {
        owner_role_relation_contract_investor_idToowner_role_relation: {
          isNot: null,
        },
      };
      this.logger.info(
        `Executando consulta com condição: ${JSON.stringify(whereCondition)}`
      );

      const [contracts, total] = await prisma.$transaction([
        prisma.contract.findMany({
          where: whereCondition,
          skip,
          take: limit,
          include: {
            pre_register: true,
            contract_advisor: {
              include: {
                owner_role_relation: {
                  include: {
                    owner: { include: { address: true } },
                    business: {
                      include: {
                        address: true,
                        owner_business_relation: {
                          include: { owner: { include: { address: true } } },
                        },
                      },
                    },
                  },
                },
              },
            },
            owner_role_relation_contract_investor_idToowner_role_relation: {
              include: {
                pre_register: true,
                owner: { include: { address: true } },
                business: {
                  include: {
                    address: true,
                    owner_business_relation: {
                      include: { owner: { include: { address: true } } },
                    },
                  },
                },
              },
            },
            owner_role_relation_contract_owner_role_relationToowner_role_relation:
              {
                include: {
                  owner: { include: { address: true } },
                  business: {
                    include: {
                      address: true,
                      owner_business_relation: {
                        include: { owner: { include: { address: true } } },
                      },
                    },
                  },
                },
              },
            addendum: {
              include: {
                income_payment_scheduled_addendum: {
                  include: {
                    income_payment_scheduled: {
                      select: { scheduled_date: true },
                    },
                  },
                },
              },
              where: { status: "FULLY_SIGNED" },
            },
          },
        }),
        prisma.contract.count(),
      ]);

      this.logger.info(
        `Consulta concluída. Total de contratos: ${total}, contratos retornados: ${contracts.length}`
      );

      const domainContracts: InvestmentContract[] = [];
      for (const contract of contracts) {
        const contractData = InvestmentContractMapper.toDomain(contract);
        if (contractData.isLeft()) {
          this.logger.error(
            `Erro ao mapear contrato. Detalhes: ${
              contractData.value
            }. Dados: ${JSON.stringify(contract)}`
          );
          continue;
        }
        domainContracts.push(contractData.value);
      }
      this.logger.info(
        `Total de contratos mapeados: ${domainContracts.length}`
      );
      return right(PaginatedResult.create(domainContracts, total, page, limit));
    } catch (error) {
      this.logger.error(
        `Erro na busca paginada de contratos para usuário ID: ${userId}. Detalhes: ${error}`
      );
      return left(error as Error);
    }
  }

  async findById(id: string): Promise<Either<Error, InvestmentContract>> {
    this.logger.info(`Buscando contrato com ID: ${id}`);
    try {
      const contract = await prisma.contract.findFirst({
        where: { id },
        include: {
          pre_register: true,
          contract_advisor: {
            include: {
              owner_role_relation: {
                include: {
                  owner: { include: { address: true, account: true } },
                  business: {
                    include: {
                      address: true,
                      account: true,
                      owner_business_relation: {
                        include: { owner: { include: { address: true } } },
                      },
                    },
                  },
                },
              },
            },
          },
          owner_role_relation_contract_investor_idToowner_role_relation: {
            include: {
              pre_register: true,
              owner: { include: { address: true, account: true } },
              business: {
                include: {
                  address: true,
                  account: true,
                  owner_business_relation: {
                    include: { owner: { include: { address: true } } },
                  },
                },
              },
            },
          },
          owner_role_relation_contract_owner_role_relationToowner_role_relation:
            true,
          addendum: {
            include: {
              income_payment_scheduled_addendum: {
                include: {
                  income_payment_scheduled: {
                    select: { scheduled_date: true },
                  },
                },
              },
            },
            where: { status: "FULLY_SIGNED" },
          },
        },
      });

      if (!contract) {
        this.logger.error(`Contrato não encontrado para ID: ${id}`);
        return left(new Error("Contrato não encontrado no banco de dados"));
      }

      const proofOfResidence = await prisma.uploads.findFirst({
        where: {
          owner: {
            cpf: contract.pre_register[0].document,
          },
          type: "PROOF_OF_RESIDENCE",
        },
      });

      const personalDocument = await prisma.uploads.findFirst({
        where: {
          owner: {
            cpf: contract.pre_register[0].document,
          },
          type: "PERSONAL_DOCUMENT",
        },
      });

      const companyDocument = await prisma.uploads.findFirst({
        where: {
          business: {
            cnpj: contract.pre_register[0].document,
          },
          type: "COMPANY_DOCUMENT",
        },
      });

      const investmentContract = InvestmentContractMapper.toDomain(
        contract,
        proofOfResidence?.url,
        companyDocument?.url,
        personalDocument?.url
      );
      if (investmentContract.isLeft()) {
        this.logger.error(
          `Erro ao mapear contrato com ID: ${id}. Detalhes: ${investmentContract.value}`
        );
        return left(investmentContract.value);
      }
      this.logger.info(`Contrato mapeado com sucesso para ID: ${id}`);
      return right(investmentContract.value);
    } catch (error) {
      this.logger.error(
        `Erro na busca do contrato com ID: ${id}. Detalhes: ${error}`
      );
      return left(error as Error);
    }
  }

  async findActiveContractsByInvestorAndYear(
    investorId: string,
    year: number
  ): Promise<Either<Error, InvestmentContract[]>> {
    this.logger.info(
      `Buscando contratos ativos para investidor ID: ${investorId} no ano: ${year}`
    );
    try {
      const contracts = await prisma.contract.findMany({
        where: {
          investor_id: investorId,
          status: "ACTIVE",
          start_contract: { gte: new Date(year, 0, 1) },
        },
        include: {
          pre_register: true,
          contract_advisor: {
            include: {
              owner_role_relation: {
                include: {
                  owner: { include: { address: true } },
                  business: {
                    include: {
                      address: true,
                      owner_business_relation: {
                        include: { owner: { include: { address: true } } },
                      },
                    },
                  },
                },
              },
            },
          },
          owner_role_relation_contract_investor_idToowner_role_relation: {
            include: {
              pre_register: true,
              owner: { include: { address: true } },
              business: {
                include: {
                  address: true,
                  owner_business_relation: {
                    include: { owner: { include: { address: true } } },
                  },
                },
              },
            },
          },
          owner_role_relation_contract_owner_role_relationToowner_role_relation:
            {
              include: {
                owner: { include: { address: true } },
                business: {
                  include: {
                    address: true,
                    owner_business_relation: {
                      include: { owner: { include: { address: true } } },
                    },
                  },
                },
              },
            },
          addendum: {
            include: {
              income_payment_scheduled_addendum: {
                include: {
                  income_payment_scheduled: {
                    select: { scheduled_date: true },
                  },
                },
              },
            },
            where: { status: "FULLY_SIGNED" },
          },
        },
      });

      this.logger.info(
        `Consulta retornou ${contracts.length} contrato(s) para investidor ID: ${investorId}`
      );
      const contractList: InvestmentContract[] = [];
      for (const contract of contracts) {
        const investmentContract = InvestmentContractMapper.toDomain(contract);
        if (investmentContract.isRight()) {
          contractList.push(investmentContract.value);
        } else {
          this.logger.error(
            `Erro ao mapear contrato durante busca ativa para investidor ID: ${investorId}. Detalhes: ${investmentContract.value}`
          );
        }
      }
      this.logger.info(
        `Total de contratos ativos mapeados: ${contractList.length}`
      );
      return right(contractList);
    } catch (error) {
      this.logger.error(
        `Erro na busca de contratos ativos para investidor ID: ${investorId}. Detalhes: ${error}`
      );
      return left(error as Error);
    }
  }

  /**
   * Salva uma entidade de contrato de investimento no banco de dados
   *
   * Este método gerencia o processo completo de criação de contrato:
   * 1. Gera número do contrato baseado no ano atual
   * 2. Cria registro do contrato com todos os dados relacionados
   * 3. Cria visualizações de carteira para corretor e assessores
   *
   * @param entity - Entidade de contrato de investimento para salvar
   * @param tx - Contexto de transação opcional
   * @returns Either com void ou Error
   */
  async save(
    entity: InvestmentContract,
    tx?: ITransactionContext
  ): Promise<Either<Error, void>> {
    try {
      this.logger.info(
        `Iniciando salvamento do contrato. contractId: ${
          entity.id
        }, investorId: ${
          entity.getInvestor().id
        }, brokerId: ${entity.getBroker()}`
      );

      const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

      // Step 1: Generate contract number
      const contractNumber = await this.generateContractNumber(client);

      // Step 2: Create contract with all related data
      await this.createContractRecord(entity, client);

      // Step 3: Create wallet views for broker and advisors
      await this.createWalletViews(entity, client);

      this.logger.info(
        `Contrato salvo com sucesso. contractId: ${entity.id}, number: ${contractNumber}`
      );
      return right(undefined);
    } catch (error) {
      this.logger.error(
        `Erro ao salvar contrato. contractId: ${entity.id}. Detalhes: ${error}`
      );
      return left(error as Error);
    }
  }

  /**
   * Gera um número de contrato baseado no ano atual
   * @param client - Cliente do banco de dados
   * @returns Número do contrato (total de contratos no ano atual + 1)
   */
  private async generateContractNumber(client: DbClient): Promise<number> {
    const currentYear = new Date().getFullYear();
    const totalContracts = await client.contract.count({
      where: {
        createdAt: {
          gte: new Date(`${currentYear}-01-01`),
          lt: new Date(`${currentYear + 1}-01-01`),
        },
      },
    });

    console.log("totalContracts", totalContracts);
    return totalContracts + 1;
  }

  /**
   * Cria o registro principal do contrato com todos os dados relacionados
   * @param entity - Entidade de contrato de investimento
   * @param client - Cliente do banco de dados
   */
  private async createContractRecord(
    entity: InvestmentContract,
    client: DbClient
  ): Promise<void> {
    const investor = entity.getInvestor();
    const address = investor.getParty().getAddress();
    const gracePeriod = this.calculateGracePeriod(entity.getStartDate());
    const { accountNumber, agency, bank, pixKey } = investor.getAccount();
    const birthDate = investor.getParty().getBirthDate();

    console.log("entity", entity);
    console.log("investor", investor);
    console.log("address", address);
    console.log("gracePeriod", gracePeriod);
    console.log("accountNumber", accountNumber);
    console.log("agency", agency);
    console.log("bank", bank);
    console.log("pixKey", pixKey);
    console.log("birthDate", birthDate);
    console.log("entity.getContractNumber()", entity.getContractNumber());
    console.log("entity.getBroker()", entity.getBroker());
    console.log("entity.getAdvisors()", entity.getAdvisors());
    console.log("entity.getContractType()", entity.getContractType());
    console.log("entity.getStatus()", entity.getStatus());
    console.log("entity.getEndDate()", entity.getEndDate());

    await client.contract.create({
      data: {
        id: entity.id,
        contract_number: entity.getContractNumber(),
        investor_id: investor.id,
        owner_role_relation: entity.getBroker(),
        contract_advisor: {
          create: entity.getAdvisors().map((advisor) => ({
            id_advisor: advisor.id,
            rate: advisor.rate,
            is_active: true,
          })),
        },
        type: entity.getContractType().valueAsString,
        status: entity.getStatus(),
        end_contract: entity.getEndDate(),
        start_contract: entity.getStartDate(),
        pre_register: {
          create: {
            adviser_id: investor.id,
            address_number: address.number,
            city: address.city,
            document: investor.getParty().getDocument(),
            dt_birth: birthDate,
            email: investor.getEmail(),
            grace_period: gracePeriod,
            investment_value: entity.getAmount().amount,
            investment_yield: entity.getProfitability().valueAsPercent,
            name: investor.getName(),
            neighborhood: "",
            phone_number: investor.getPhone(),
            status: "",
            zip_code: address.postalCode,
            rg: investor.isIndividual()
              ? (investor.getParty() as Individual).getRg()
              : undefined,
            purchase_with: entity.getPaymentMethod().value,
            account: accountNumber,
            bank,
            agency,
            pix: pixKey,
            amount_quotes: entity.getQuotaQuantity(),
          },
        },
      },
    });
  }

  /**
   * Calcula período de carência (data de início + 1 dia)
   * @param startDate - Data de início do contrato
   * @returns Data do período de carência
   */
  private calculateGracePeriod(startDate: Date): Date {
    const gracePeriod = new Date(startDate);
    gracePeriod.setDate(gracePeriod.getDate() + 1);
    return gracePeriod;
  }

  /**
   * Cria visualizações de carteira para corretor e assessores
   * @param entity - Entidade de contrato de investimento
   * @param client - Cliente do banco de dados
   */
  private async createWalletViews(
    entity: InvestmentContract,
    client: DbClient
  ): Promise<void> {
    const investor = entity.getInvestor();

    await client.wallets_views.createMany({
      data: [
        {
          upper_id: entity.getBroker(),
          bottom_id: investor.id,
        },
        ...entity.getAdvisors().map((advisor) => ({
          upper_id: advisor.id,
          bottom_id: investor.id,
        })),
      ],
      skipDuplicates: true,
    });
  }

  findAll(
    filters?: ContractFilters | undefined,
    page?: number,
    limit?: number
  ): Promise<Either<Error, InvestmentContract[]>> {
    throw new Error("Method not implemented.");
  }
  delete(id: string): Promise<Either<Error, void>> {
    throw new Error("Method not implemented.");
  }
  findActiveByInvestor(investorId: string): Promise<InvestmentContract | null> {
    throw new Error("Method not implemented.");
  }
  async findByStatus(
    status: ContractStatus
  ): Promise<{ id: string; status: ContractStatus }[]> {
    const contracts = await prisma.contract.findMany({
      where: { status },
      select: { id: true, status: true },
    });

    return contracts.map((contract) => ({
      id: contract.id,
      status: ContractStatusMapper.fromString(contract.status ?? ""),
    }));
  }

  async findByInvestorAndStatus(
    investorId: string,
    status: ContractStatus
  ): Promise<Either<Error, InvestmentContract[]>> {
    try {
      this.logger.info(
        `Buscando contratos para investidor ${investorId} com status ${status}`
      );

      const contracts = await prisma.contract.findMany({
        where: {
          investor_id: investorId,
          status: status,
        },
        include: {
          pre_register: true,
          contract_advisor: {
            include: {
              owner_role_relation: {
                include: {
                  owner: { include: { address: true, account: true } },
                  business: {
                    include: {
                      address: true,
                      account: true,
                      owner_business_relation: {
                        include: { owner: { include: { address: true } } },
                      },
                    },
                  },
                },
              },
            },
          },
          owner_role_relation_contract_investor_idToowner_role_relation: {
            include: {
              pre_register: true,
              owner: { include: { address: true, account: true } },
              business: {
                include: {
                  address: true,
                  account: true,
                  owner_business_relation: {
                    include: { owner: { include: { address: true } } },
                  },
                },
              },
            },
          },
          owner_role_relation_contract_owner_role_relationToowner_role_relation:
            true,
          addendum: {
            include: {
              income_payment_scheduled_addendum: {
                include: {
                  income_payment_scheduled: {
                    select: { scheduled_date: true },
                  },
                },
              },
            },
            where: { status: "FULLY_SIGNED" },
          },
        },
      });

      const domainContracts = contracts.map((contract) =>
        InvestmentContractMapper.toDomain(contract)
      );

      this.logger.info(
        `Encontrados ${domainContracts.length} contratos para investidor ${investorId} com status ${status}`
      );

      return right(domainContracts);
    } catch (error) {
      this.logger.error(
        `Erro ao buscar contratos por investidor e status: ${error}`
      );
      return left(error as Error);
    }
  }
  isAdvisorLinkedToBroker(
    advisorId: string,
    brokerId: string
  ): Promise<boolean> {
    throw new Error("Method not implemented.");
  }
  async findIncomeReportByInvestorId(
    investorId: string,
    year: number
  ): Promise<IncomeReportEmail | null> {
    this.logger.info(
      `Buscando relatório de renda para investidor ID: ${investorId} no ano: ${year}`
    );
    const incomeReport = await prisma.income_report.findFirst({
      where: {
        investor_id: investorId,
        reference_year: String(year),
      },
      include: {
        files: true,
        owner_role_relation: {
          include: {
            owner: true,
          },
        },
        income_report_email: {
          include: {
            email: true,
          },
        },
      },
    });

    if (!incomeReport) {
      this.logger.info(
        `Relatório de renda não encontrado para investidor ID: ${investorId} no ano: ${year}`
      );
      return null;
    }

    if (!incomeReport.files?.url) {
      this.logger.info(
        `Relatório de renda encontrado, mas sem arquivo para investidor ID: ${investorId}`
      );
      return null;
    }

    if (!incomeReport.owner_role_relation?.owner) {
      this.logger.info(
        `Relatório de renda sem proprietário definido para investidor ID: ${investorId}`
      );
      return null;
    }

    const emailIncomeReport = IncomeReportEmail.createNew(
      {
        to_email: incomeReport.owner_role_relation?.owner?.email,
        template_data: {
          calendar_year: year,
          download_link: incomeReport.files?.url,
          email: incomeReport.owner_role_relation?.owner?.email,
          name: incomeReport.owner_role_relation?.owner?.name,
          telephone: incomeReport.owner_role_relation?.owner?.email,
        },
      },
      incomeReport.id
    );

    this.logger.info(
      `Relatório de renda encontrado e mapeado para investidor ID: ${investorId}`
    );
    return emailIncomeReport;
  }

  async editContract(
    contractId: string,
    data: InvestmentContract,
    tx?: ITransactionContext
  ): Promise<Either<Error, void>> {
    try {
      const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

      const investor = data.getInvestor();
      const party = investor.getParty();

      this.logger.info(`Iniciando edição de contrato ${contractId}`);

      const existingContract = await client.contract.findUnique({
        where: { id: contractId },
        include: {
          pre_register: true,
          owner_role_relation_contract_investor_idToowner_role_relation: {
            include: {
              owner: { include: { address: true } },
              business: { include: { address: true } },
            },
          },
        },
      });

      if (!existingContract) {
        return left(new Error("Contrato não encontrado"));
      }

      const ownerRelation =
        existingContract.owner_role_relation_contract_investor_idToowner_role_relation;

      const mainAddress = data.getInvestor().getParty().getAddress();
      const mainAddressData = {
        street: mainAddress.street,
        number: mainAddress.number,
        neighborhood: mainAddress.neighborhood,
        city: mainAddress.city,
        state: mainAddress.state,
        cep: mainAddress.postalCode,
        complement: mainAddress.complement || null,
      };

      if (party instanceof Individual) {
        const ownerId = ownerRelation?.owner_id;
        if (ownerId) {
          const existingAddress = await client.address.findFirst({
            where: { owner_id: ownerId },
          });
          if (existingAddress) {
            await client.address.update({
              where: { id: existingAddress.id },
              data: mainAddressData,
            });
            this.logger.info(
              `Endereço do Investidor PF atualizado: ${ownerId}`
            );
          } else {
            this.logger.info(
              `Endereço não encontrado para Investidor PF: ${ownerId}, criação não implementada neste fluxo.`
            );
          }
        }
      } else if (party instanceof Company) {
        const businessId = ownerRelation?.business_id;
        if (businessId) {
          const existingAddress = await client.address.findFirst({
            where: { business_id: businessId },
          });
          if (existingAddress) {
            await client.address.update({
              where: { id: existingAddress.id },
              data: mainAddressData,
            });
            this.logger.info(`Endereço da Empresa atualizado: ${businessId}`);
          } else {
            this.logger.info(
              `Endereço não encontrado para Empresa: ${businessId}, criação não implementada neste fluxo.`
            );
          }

          const representative = party.getLegalRepresentative();
          const representativeAddress = representative.getAddress();
          const representativeOwnerId = ownerRelation?.business?.owner_id; // ID do owner (representante)

          if (representativeOwnerId) {
            const representativeAddressData = {
              street: representativeAddress.street,
              number: representativeAddress.number,
              neighborhood: representativeAddress.neighborhood,
              city: representativeAddress.city,
              state: representativeAddress.state,
              cep: representativeAddress.postalCode,
              complement: representativeAddress.complement || null,
            };
            const existingRepAddress = await client.address.findFirst({
              where: { owner_id: representativeOwnerId },
            });
            if (existingRepAddress) {
              await client.address.update({
                where: { id: existingRepAddress.id },
                data: representativeAddressData,
              });
              this.logger.info(
                `Endereço do Representante Legal atualizado: ${representativeOwnerId}`
              );
            } else {
              this.logger.info(
                `Endereço não encontrado para Representante Legal: ${representativeOwnerId}, atualização ignorada.`
              );
            }
          } else {
            this.logger.error(
              `ID do Representante Legal não encontrado no relacionamento para atualizar endereço. Business ID: ${businessId}`
            );
          }
        }
      }

      if (
        existingContract
          .owner_role_relation_contract_investor_idToowner_role_relation
          ?.business &&
        party instanceof Company
      ) {
        await client.business.update({
          where: {
            id:
              existingContract
                .owner_role_relation_contract_investor_idToowner_role_relation
                ?.business?.id ?? "",
          },
          data: {
            fantasy_name: party.getName(),
            company_name: party.getName(),
            cnpj: investor.getParty().getDocument(),
            type: party.getCompanyType().toString(),
          },
        });

        await client.owner.update({
          where: {
            id:
              existingContract
                .owner_role_relation_contract_investor_idToowner_role_relation
                ?.business?.owner_id ?? "",
          },
          data: {
            name: party.getLegalRepresentative().getName(),
            email: party.getLegalRepresentative().getEmail(),
            phone: party.getLegalRepresentative().getPhone(),
            cpf: party.getLegalRepresentative().getDocument(),
            rg: party.getLegalRepresentative().getRg(),
            dt_birth: party.getLegalRepresentative().getBirthDate(),
            mother_name: party.getLegalRepresentative().getMotherName(),
            occupation: party.getLegalRepresentative().getOccupation(),
            issuingAgency: party.getLegalRepresentative().getIssuingAgency?.(),
            nationality: party.getLegalRepresentative().getNationality?.(),
          },
        });
      }

      if (
        existingContract
          .owner_role_relation_contract_investor_idToowner_role_relation
          ?.owner &&
        party instanceof Individual
      ) {
        await client.owner.update({
          where: {
            id:
              existingContract
                .owner_role_relation_contract_investor_idToowner_role_relation
                ?.owner?.id ?? "",
          },
          data: {
            name: party.getName(),
            email: party.getEmail(),
            phone: party.getPhone(),
            cpf: party.getDocument(),
            rg: party.getRg(),
            dt_birth: party.getBirthDate(),
            mother_name: party.getMotherName(),
            occupation: party.getOccupation(),
            issuingAgency: party.getIssuingAgency?.(),
            nationality: party.getNationality?.(),
          },
        });
      }

      await client.contract.update({
        where: { id: contractId },
        data: {
          status: data.getStatus(),
          external_id: data.getSignatureRequestId()
            ? Number(data.getSignatureRequestId())
            : null,
          contract_pdf: data.getContractUrl(),
          proof_payment: data.getProofPayment(),
          type: data.getContractType().valueAsString,
          is_debenture: data.getIsDebenture(),
          start_contract: data.getStartDate(),
          end_contract: data.getEndDate(),
          duration_in_months: data.getDurationInMonths(),
        },
      });

      const { accountNumber, agency, bank, pixKey } = investor.getAccount();

      const preRegisterUpdateData = {
        investment_value: data.getAmount().amount,
        investment_yield: data.getProfitability().valueAsPercent,
        email: investor.getEmail(),
        name: investor.getName(),
        phone_number: investor.getPhone(),
        document: investor.getParty().getDocument(),
        mother_name: investor.getParty().getMotherName(),
        status: "ACTIVE",
        account: accountNumber,
        bank,
        agency,
        pix: pixKey,
        rg: investor.isIndividual()
          ? (investor.getParty() as Individual).getRg()
          : undefined,
        dt_birth: investor.isIndividual()
          ? (investor.getParty() as Individual).getBirthDate()
          : undefined,
        purchase_with: data.getPaymentMethod().value,
        amount_quotes: data.getQuotaQuantity(),
      };

      await client.pre_register.update({
        where: { id: existingContract.pre_register[0].id },
        data: preRegisterUpdateData,
      });

      return right(undefined);
    } catch (error) {
      this.logger.error(
        `Erro ao editar contrato. contractId: ${contractId}. Detalhes: ${error}`
      );
      return left(error as Error);
    }
  }
}
